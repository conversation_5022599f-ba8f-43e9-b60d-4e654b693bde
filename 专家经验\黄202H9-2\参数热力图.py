import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib
import pandas as pd

# 设置中文字体（SimHei 是常见的黑体中文字体）
plt.rcParams['font.sans-serif'] = ['SimHei']       # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False         # 正常显示负号

# 读取数据
df = pd.read_csv("test黄202H9-2.csv", encoding='gbk')

# 选择参数列
parameter_cols = ['DEP', 'BITDEP', 'HOKHEI',  'HKLD',
                  'RPM', 'TOR', 'FLOWIN', 'FLOWOUT', 'SPP']

# 计算相关性
corr_matrix = df[parameter_cols].corr()

# 绘图
plt.figure(figsize=(10, 8))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)
plt.title("参数相关性热力图")  # 中文标题
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()
