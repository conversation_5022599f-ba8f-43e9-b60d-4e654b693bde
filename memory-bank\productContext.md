# Product Context

## Project Overview
基于深度学习的时间序列异常检测系统，专门用于钻井过程中的卡钻检测。系统采用多种Transformer架构模型（TimesNet、FEDformer、Autoformer等）进行时间序列分析，结合专业的卡钻检测算法，实现对钻井参数的实时监控和异常预警。

## Project Goal
- 实现钻井过程中卡钻事件的自动检测和预警
- 提供高精度的时间序列异常检测能力
- 支持多种深度学习模型的训练和推理
- 提供完整的数据处理和结果分析流程

## Key Features
- 多模型支持：TimesNet、FEDformer、Autoformer、Transformer等
- 专业卡钻检测：基于扭矩和大钩负荷的经验检测算法
- GPU加速：支持CUDA加速计算，提高处理效率
- 缓存机制：训练集重建误差缓存，避免重复计算
- 灵活数据格式：支持.npy和.csv格式，兼容多种时间戳格式
- 无标签模式：支持仅预测不评估的应用场景

## Overall Architecture
- 数据层：data_provider模块负责数据加载和预处理
- 模型层：models目录包含各种深度学习模型实现
- 实验层：exp模块管理训练和测试流程
- 工具层：utils提供损失函数、评估指标、专业检测器等
- 接口层：run.py提供统一的命令行接口

## Technology Stack
- Python 3.7+, PyTorch 1.8+, NumPy, Pandas, Scikit-learn
- CUDA支持（可选）, Scipy（信号处理）

## Target Users
- 钻井工程师和技术人员
- 石油天然气行业的数据分析师
- 工业物联网系统集成商

2025-08-07 15:37:33 - 初始化项目记忆库
2025-08-07 15:40:00 - 完成异常检测模块技术规范分析