

import os
import numpy as np
import pandas as pd
import glob
import re
import torch
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, LabelEncoder
from utils.timefeatures import time_features
from data_provider.m4 import M4Dataset, M4Meta
from data_provider.uea import subsample, interpolate_missing, Normalizer
from sktime.datasets import load_from_tsfile_to_dataframe

import warnings

warnings.filterwarnings('ignore')


class Dataset_ETT_hour(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=True, timeenc=0, freq='h', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        border1s = [0, 12 * 30 * 24 - self.seq_len, 12 * 30 * 24 + 4 * 30 * 24 - self.seq_len]
        border2s = [12 * 30 * 24, 12 * 30 * 24 + 4 * 30 * 24, 12 * 30 * 24 + 8 * 30 * 24]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0) 

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_ETT_minute(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTm1.csv',
                 target='OT', scale=True, timeenc=0, freq='t', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        border1s = [0, 12 * 30 * 24 * 4 - self.seq_len, 12 * 30 * 24 * 4 + 4 * 30 * 24 * 4 - self.seq_len]
        border2s = [12 * 30 * 24 * 4, 12 * 30 * 24 * 4 + 4 * 30 * 24 * 4, 12 * 30 * 24 * 4 + 8 * 30 * 24 * 4]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            df_stamp['minute'] = df_stamp.date.apply(lambda row: row.minute, 1)
            df_stamp['minute'] = df_stamp.minute.map(lambda x: x // 15)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0)

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_Custom(Dataset):
    def __init__(self, root_path, flag='train', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=True, timeenc=0, freq='h', seasonal_patterns=None):
        # size [seq_len, label_len, pred_len]
        # info
        if size == None:
            self.seq_len = 24 * 4 * 4
            self.label_len = 24 * 4
            self.pred_len = 24 * 4
        else:
            self.seq_len = size[0]
            self.label_len = size[1]
            self.pred_len = size[2]
        # init
        assert flag in ['train', 'test', 'val']
        type_map = {'train': 0, 'val': 1, 'test': 2}
        self.set_type = type_map[flag]

        self.features = features
        self.target = target
        self.scale = scale
        self.timeenc = timeenc
        self.freq = freq

        self.root_path = root_path
        self.data_path = data_path
        self.__read_data__()

    def __read_data__(self):
        self.scaler = StandardScaler()
        df_raw = pd.read_csv(os.path.join(self.root_path,
                                          self.data_path))

        '''
        df_raw.columns: ['date', ...(other features), target feature]
        '''
        cols = list(df_raw.columns)
        cols.remove(self.target)
        cols.remove('date')
        df_raw = df_raw[['date'] + cols + [self.target]]
        num_train = int(len(df_raw) * 0.7)
        num_test = int(len(df_raw) * 0.2)
        num_vali = len(df_raw) - num_train - num_test
        border1s = [0, num_train - self.seq_len, len(df_raw) - num_test - self.seq_len]
        border2s = [num_train, num_train + num_vali, len(df_raw)]
        border1 = border1s[self.set_type]
        border2 = border2s[self.set_type]

        if self.features == 'M' or self.features == 'MS':
            cols_data = df_raw.columns[1:]
            df_data = df_raw[cols_data]
        elif self.features == 'S':
            df_data = df_raw[[self.target]]

        if self.scale:
            train_data = df_data[border1s[0]:border2s[0]]
            self.scaler.fit(train_data.values)
            data = self.scaler.transform(df_data.values)
        else:
            data = df_data.values

        df_stamp = df_raw[['date']][border1:border2]
        df_stamp['date'] = pd.to_datetime(df_stamp.date)
        if self.timeenc == 0:
            df_stamp['month'] = df_stamp.date.apply(lambda row: row.month, 1)
            df_stamp['day'] = df_stamp.date.apply(lambda row: row.day, 1)
            df_stamp['weekday'] = df_stamp.date.apply(lambda row: row.weekday(), 1)
            df_stamp['hour'] = df_stamp.date.apply(lambda row: row.hour, 1)
            data_stamp = df_stamp.drop(['date'], 1).values
        elif self.timeenc == 1:
            data_stamp = time_features(pd.to_datetime(df_stamp['date'].values), freq=self.freq)
            data_stamp = data_stamp.transpose(1, 0)

        self.data_x = data[border1:border2]
        self.data_y = data[border1:border2]
        self.data_stamp = data_stamp

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data_x[s_begin:s_end]
        seq_y = self.data_y[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def __len__(self):
        return len(self.data_x) - self.seq_len - self.pred_len + 1

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)


class Dataset_M4(Dataset):
    def __init__(self, root_path, flag='pred', size=None,
                 features='S', data_path='ETTh1.csv',
                 target='OT', scale=False, inverse=False, timeenc=0, freq='15min',
                 seasonal_patterns='Yearly'):
        # size [seq_len, label_len, pred_len]
        # init
        self.features = features
        self.target = target
        self.scale = scale
        self.inverse = inverse
        self.timeenc = timeenc
        self.root_path = root_path

        self.seq_len = size[0]
        self.label_len = size[1]
        self.pred_len = size[2]

        self.seasonal_patterns = seasonal_patterns
        self.history_size = M4Meta.history_size[seasonal_patterns]
        self.window_sampling_limit = int(self.history_size * self.pred_len)
        self.flag = flag

        self.__read_data__()

    def __read_data__(self):
        # M4Dataset.initialize()
        if self.flag == 'train':
            dataset = M4Dataset.load(training=True, dataset_file=self.root_path)
        else:
            dataset = M4Dataset.load(training=False, dataset_file=self.root_path)
        training_values = np.array(
            [v[~np.isnan(v)] for v in
             dataset.values[dataset.groups == self.seasonal_patterns]])  # split different frequencies
        self.ids = np.array([i for i in dataset.ids[dataset.groups == self.seasonal_patterns]])
        self.timeseries = [ts for ts in training_values]

    def __getitem__(self, index):
        insample = np.zeros((self.seq_len, 1))
        insample_mask = np.zeros((self.seq_len, 1))
        outsample = np.zeros((self.pred_len + self.label_len, 1))
        outsample_mask = np.zeros((self.pred_len + self.label_len, 1))  # m4 dataset

        sampled_timeseries = self.timeseries[index]
        cut_point = np.random.randint(low=max(1, len(sampled_timeseries) - self.window_sampling_limit),
                                      high=len(sampled_timeseries),
                                      size=1)[0]

        insample_window = sampled_timeseries[max(0, cut_point - self.seq_len):cut_point]
        insample[-len(insample_window):, 0] = insample_window
        insample_mask[-len(insample_window):, 0] = 1.0
        outsample_window = sampled_timeseries[
                           cut_point - self.label_len:min(len(sampled_timeseries), cut_point + self.pred_len)]
        outsample[:len(outsample_window), 0] = outsample_window
        outsample_mask[:len(outsample_window), 0] = 1.0
        return insample, outsample, insample_mask, outsample_mask

    def __len__(self):
        return len(self.timeseries)

    def inverse_transform(self, data):
        return self.scaler.inverse_transform(data)

    def last_insample_window(self):
        """
        The last window of insample size of all timeseries.
        This function does not support batching and does not reshuffle timeseries.

        :return: Last insample window of all timeseries. Shape "timeseries, insample size"
        """
        insample = np.zeros((len(self.timeseries), self.seq_len))
        insample_mask = np.zeros((len(self.timeseries), self.seq_len))
        for i, ts in enumerate(self.timeseries):
            ts_last_window = ts[-self.seq_len:]
            insample[i, -len(ts):] = ts_last_window
            insample_mask[i, -len(ts):] = 1.0
        return insample, insample_mask


class PSMSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = pd.read_csv(os.path.join(root_path, 'train.csv'))
        data = data.values[:, 1:]
        data = np.nan_to_num(data)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = pd.read_csv(os.path.join(root_path, 'test.csv'))
        test_data = test_data.values[:, 1:]
        test_data = np.nan_to_num(test_data)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = pd.read_csv(os.path.join(root_path, 'test_label.csv')).values[:, 1:]
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class MSLSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "MSL_train.npy"))
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "MSL_test.npy"))
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "MSL_test_label.npy"))
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])

class Stuck1001Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "1001_train1.npy"),allow_pickle=True)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "1001_test.npy"),allow_pickle=True)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "1001_test_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])

class StuckGSH3Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "GS-H3ok_train.npy"),allow_pickle=True)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "GS-H3ok_test.npy"),allow_pickle=True)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "GS-H3ok_test_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])



class Ning209H543Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "宁209H54-3_train.npy"),allow_pickle=True)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "宁209H54-3_test.npy"),allow_pickle=True)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "宁209H54-3_test_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])

class Ning209H83Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "宁209H8-3钻井日志_train.npy"),allow_pickle=True)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "宁209H8-3钻井日志_test.npy"),allow_pickle=True)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "宁209H8-3钻井日志_test_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])

class Ning209H203Loader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "宁209H20-3_train.npy"),allow_pickle=True)
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "宁209H20-3_test.npy"),allow_pickle=True)
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "宁209H20-3_test_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])

class FluidLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "蓬深正常.npy"),allow_pickle=True)
        data = data[:, 1:]
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "蓬深101.npy"), allow_pickle=True)
        test_data = test_data[:, 1:]
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "蓬深101_label.npy"),allow_pickle=True)
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SMAPSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "SMAP_train.npy"))
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "SMAP_test.npy"))
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "SMAP_test_label.npy"))
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):

        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SMDSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=100, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()
        data = np.load(os.path.join(root_path, "SMD_train.npy"))
        self.scaler.fit(data)
        data = self.scaler.transform(data)
        test_data = np.load(os.path.join(root_path, "SMD_test.npy"))
        self.test = self.scaler.transform(test_data)
        self.train = data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = np.load(os.path.join(root_path, "SMD_test_label.npy"))

    def __len__(self):
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


class SWATSegLoader(Dataset):
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.scaler = StandardScaler()

        train_data = pd.read_csv(os.path.join(root_path, 'swat_train2.csv'))
        test_data = pd.read_csv(os.path.join(root_path, 'swat2.csv'))
        labels = test_data.values[:, -1:]
        train_data = train_data.values[:, :-1]
        test_data = test_data.values[:, :-1]

        self.scaler.fit(train_data)
        train_data = self.scaler.transform(train_data)
        test_data = self.scaler.transform(test_data)
        self.train = train_data
        self.test = test_data
        data_len = len(self.train)
        self.val = self.train[(int)(data_len * 0.8):]
        self.test_labels = labels
        print("test:", self.test.shape)
        print("train:", self.train.shape)

    def __len__(self):
        """
        Number of images in the object dataset.
        """
        if self.flag == "train":
            return (self.train.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'val'):
            return (self.val.shape[0] - self.win_size) // self.step + 1
        elif (self.flag == 'test'):
            return (self.test.shape[0] - self.win_size) // self.step + 1
        else:
            return (self.test.shape[0] - self.win_size) // self.win_size + 1

    def __getitem__(self, index):
        index = index * self.step
        if self.flag == "train":
            return np.float32(self.train[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'val'):
            return np.float32(self.val[index:index + self.win_size]), np.float32(self.test_labels[0:self.win_size])
        elif (self.flag == 'test'):
            return np.float32(self.test[index:index + self.win_size]), np.float32(
                self.test_labels[index:index + self.win_size])
        else:
            return np.float32(self.test[
                              index // self.step * self.win_size:index // self.step * self.win_size + self.win_size]), np.float32(
                self.test_labels[index // self.step * self.win_size:index // self.step * self.win_size + self.win_size])


from scipy.io import loadmat
class Data1001(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        """
        训练数据集与测试数据集的Dataset对象
        :param path: 数据集路径
        :param dataset: 区分是获得训练集还是测试集
        """
        super(Data1001, self).__init__()
        self.root_path = root_path
        self.flag = flag  # 选择获取测试集还是训练集
        self.train_len, \
        self.test_len, \
        self.input_len, \
        self.channel_len, \
        self.output_len, \
        self.train_dataset, \
        self.train_label, \
        self.test_dataset, \
        self.test_label, \
        self.max_length_sample_inTest, \
        self.train_dataset_with_no_paddding = self.pre_option1001(root_path)
        self.max_seq_len = self.train_len
        self.feature_df = self.train_dataset

    def __getitem__(self, index):
        if self.flag == 'TRAIN':
            return self.train_dataset[index], self.train_label[index]
        elif self.flag == 'TEST':
            return self.test_dataset[index], self.test_label[index]

    def __len__(self):
        if self.flag == 'TRAIN':
            return self.train_len
        elif self.flag == 'TEST':
            return self.test_len

    # 数据预处理
    def pre_option1001(self, root_path: str):
        """
        数据预处理  由于每个样本的时间步维度不同，在此使用最长的时间步作为时间步的维度，使用0进行填充
        :param path: 数据集路径
        :return: 训练集样本数量，测试集样本数量，时间步维度，通道数，分类数，训练集数据，训练集标签，测试集数据，测试集标签，测试集中时间步最长的样本列表，没有padding的训练集数据
        """
        m = loadmat(root_path)

        # m中是一个字典 有4个key 其中最后一个键值对存储的是数据
        x1, x2, x3, x4, x5, x6, x7 = m
        train_data = m[x4][0]
        train_label = m[x5][0]
        test_data = m[x6][0]
        test_label = m[x7][0]

        train_label = train_label.squeeze()
        train_label = torch.Tensor(train_label)
        test_label = test_label.squeeze()
        test_label = torch.Tensor(test_label)

        train_dataset_with_no_paddding = []
        test_dataset_with_no_paddding = []
        train_dataset = []
        test_dataset = []

        for x1 in train_data:
            train_dataset_with_no_paddding.append(x1.transpose(-1, -2).tolist())
            x1 = torch.as_tensor(x1).float()
            train_dataset.append(x1)
        train_dataset = torch.stack(train_dataset, dim=0)

        for index, x2 in enumerate(test_data):
            test_dataset_with_no_paddding.append(x2.transpose(-1, -2).tolist())
            x2 = torch.as_tensor(x2).float()
            test_dataset.append(x2)
        test_dataset = torch.stack(test_dataset, dim=0)

        train_len = train_dataset.shape[0]
        test_len = test_dataset.shape[0]
        channel = test_dataset[0].shape[-1]
        input = test_dataset[0].shape[-2]
        output_len = len(tuple(set(train_label)))
        max_length_sample_inTest = test_dataset.shape[1]

        return train_len, test_len, input, channel, output_len, train_dataset, train_label, test_dataset, test_label, max_length_sample_inTest, train_dataset_with_no_paddding


class UEAloader(Dataset):
    """
    Dataset class for datasets included in:
        Time Series Classification Archive (www.timeseriesclassification.com)
    Argument:
        limit_size: float in (0, 1) for debug
    Attributes:
        all_df: (num_samples * seq_len, num_columns) dataframe indexed by integer indices, with multiple rows corresponding to the same index (sample).
            Each row is a time step; Each column contains either metadata (e.g. timestamp) or a feature.
        feature_df: (num_samples * seq_len, feat_dim) dataframe; contains the subset of columns of `all_df` which correspond to selected features
        feature_names: names of columns contained in `feature_df` (same as feature_df.columns)
        all_IDs: (num_samples,) series of IDs contained in `all_df`/`feature_df` (same as all_df.index.unique() )
        labels_df: (num_samples, num_labels) pd.DataFrame of label(s) for each sample
        max_seq_len: maximum sequence (time series) length. If None, script argument `max_seq_len` will be used.
            (Moreover, script argument overrides this attribute)
    """

    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.root_path = root_path
        self.all_df, self.labels_df = self.load_all(root_path, file_list=file_list, flag=flag)
        self.all_IDs = self.all_df.index.unique()  # all sample IDs (integer indices 0 ... num_samples-1)

        if limit_size is not None:
            if limit_size > 1:
                limit_size = int(limit_size)
            else:  # interpret as proportion if in (0, 1]
                limit_size = int(limit_size * len(self.all_IDs))
            self.all_IDs = self.all_IDs[:limit_size]
            self.all_df = self.all_df.loc[self.all_IDs]

        # use all features
        self.feature_names = self.all_df.columns
        self.feature_df = self.all_df

        # pre_process
        normalizer = Normalizer()
        self.feature_df = normalizer.normalize(self.feature_df)
        print(len(self.all_IDs))

    def load_all(self, root_path, file_list=None, flag=None):
        """
        Loads datasets from csv files contained in `root_path` into a dataframe, optionally choosing from `pattern`
        Args:
            root_path: directory containing all individual .csv files
            file_list: optionally, provide a list of file paths within `root_path` to consider.
                Otherwise, entire `root_path` contents will be used.
        Returns:
            all_df: a single (possibly concatenated) dataframe with all data corresponding to specified files
            labels_df: dataframe containing label(s) for each sample
        """
        # Select paths for training and evaluation
        if file_list is None:
            data_paths = glob.glob(os.path.join(root_path, '*'))  # list of all paths
        else:
            data_paths = [os.path.join(root_path, p) for p in file_list]
        if len(data_paths) == 0:
            raise Exception('No files found using: {}'.format(os.path.join(root_path, '*')))
        if flag is not None:
            data_paths = list(filter(lambda x: re.search(flag, x), data_paths))
        input_paths = [p for p in data_paths if os.path.isfile(p) and p.endswith('.ts')]
        if len(input_paths) == 0:
            pattern='*.ts'
            raise Exception("No .ts files found using pattern: '{}'".format(pattern))

        all_df, labels_df = self.load_single(input_paths[0])  # a single file contains dataset

        return all_df, labels_df

    def load_single(self, filepath):
        df, labels = load_from_tsfile_to_dataframe(filepath, return_separate_X_and_y=True,
                                                             replace_missing_vals_with='NaN')
        labels = pd.Series(labels, dtype="category")
        self.class_names = labels.cat.categories
        labels_df = pd.DataFrame(labels.cat.codes,
                                 dtype=np.int8)  # int8-32 gives an error when using nn.CrossEntropyLoss

        lengths = df.applymap(
            lambda x: len(x)).values  # (num_samples, num_dimensions) array containing the length of each series

        horiz_diffs = np.abs(lengths - np.expand_dims(lengths[:, 0], -1))

        if np.sum(horiz_diffs) > 0:  # if any row (sample) has varying length across dimensions
            df = df.applymap(subsample)

        lengths = df.applymap(lambda x: len(x)).values
        vert_diffs = np.abs(lengths - np.expand_dims(lengths[0, :], 0))
        if np.sum(vert_diffs) > 0:  # if any column (dimension) has varying length across samples
            self.max_seq_len = int(np.max(lengths[:, 0]))
        else:
            self.max_seq_len = lengths[0, 0]

        # First create a (seq_len, feat_dim) dataframe for each sample, indexed by a single integer ("ID" of the sample)
        # Then concatenate into a (num_samples * seq_len, feat_dim) dataframe, with multiple rows corresponding to the
        # sample index (i.e. the same scheme as all datasets in this project)

        df = pd.concat((pd.DataFrame({col: df.loc[row, col] for col in df.columns}).reset_index(drop=True).set_index(
            pd.Series(lengths[row, 0] * [row])) for row in range(df.shape[0])), axis=0)

        # Replace NaN values
        grp = df.groupby(by=df.index)
        df = grp.transform(interpolate_missing)

        return df, labels_df

    def instance_norm(self, case):
        if self.root_path.count('EthanolConcentration') > 0:  # special process for numerical stability
            mean = case.mean(0, keepdim=True)
            case = case - mean
            stdev = torch.sqrt(torch.var(case, dim=1, keepdim=True, unbiased=False) + 1e-5)
            case /= stdev
            return case
        else:
            return case

    def __getitem__(self, ind):
        return self.instance_norm(torch.from_numpy(self.feature_df.loc[self.all_IDs[ind]].values)), \
               torch.from_numpy(self.labels_df.loc[self.all_IDs[ind]].values)

    def __len__(self):
        return len(self.all_IDs)

#
# from sklearn.model_selection import train_test_split
# from sklearn.preprocessing import LabelEncoder, StandardScaler
# class EarlysignaldetLoader(Dataset):
#     def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
#         self.flag = flag
#         # Step 1: 获取所有文件路径
#         self.file_paths, self.labels = self.get_file_paths_and_labels(root_path)
#
#         # Step 2: 收集所有文件中的CW和RIGSTA值，统一训练LabelEncoder
#         self.cw_values, self.rigsta_values = self.collect_categorical_values(self.file_paths)
#
#         # 初始化LabelEncoder并训练
#         self.cw_encoder = LabelEncoder()
#         self.rigsta_encoder = LabelEncoder()
#         self.cw_encoder.fit(self.cw_values)
#         self.rigsta_encoder.fit(self.rigsta_values)
#
#         self.max_seq_len = self.get_max_length(self.file_paths)
#
#         self.train_file, self.test_file, self.train_labels, self.test_labels = train_test_split(
#             self.file_paths, self.labels, test_size=0.2, random_state=42
#         )
#         if self.flag == 'Train':
#             self.file_paths = self.train_file
#             self.labels = self.train_labels
#             self.class_names = ['normal', 'earlysignal']
#         else:
#             self.file_paths = self.test_file
#             self.labels = self.test_labels
#             self.class_names = ['normal', 'earlysignal']
#         self.scaler = StandardScaler()
#         # self.transform = transform
#
#     # 获取文件路径和标签
#     def get_file_paths_and_labels(self, base_folder):
#         file_paths = []
#         labels = []
#         for class_label, class_folder in enumerate(['normal', '宁卡钻']):
#             class_folder_path = os.path.join(base_folder, class_folder)
#             for file_name in os.listdir(class_folder_path):
#                 file_paths.append(os.path.join(class_folder_path, file_name))
#                 labels.append(class_label)
#         return file_paths, labels
#
#     # 定义数据加载函数
#     def load_time_series_data(self, file_path):
#         data = pd.read_csv(file_path)
#         # 初始化 non_temp 为与 data 行数一致的两列全零
#         non_temp = pd.DataFrame(np.zeros((data.shape[0], 2)), columns=['CW', 'RIGSTA'])
#         if 'CSIP' in data.columns:
#             data = data.drop(columns=['CSIP'])
#         if 'CW' in data.columns and 'RIGSTA' in data.columns:
#             # 使用已训练好的LabelEncoder对CW和RIGSTA进行编码
#             data['CW'] = self.cw_encoder.transform(data['CW'].fillna(method='ffill'))
#             # 将RIGSTA列转换为字符串类型，并填充缺失值
#             data['RIGSTA'] = data['RIGSTA'].astype(str).fillna(method='ffill')
#
#             # 使用已训练好的LabelEncoder对RIGSTA进行编码
#             data['RIGSTA'] = self.rigsta_encoder.transform(data['RIGSTA'])
#             CW = data['CW']
#             RIGSTA = data['RIGSTA']
#             data = data.drop(columns=['CW', 'RIGSTA'])
#             non_temp = pd.concat([CW, RIGSTA], axis=1)
#         data = data.drop(['date'], axis=1)
#         time_series = data.values
#         return time_series, non_temp
#
#     # 收集所有文件中的CW和RIGSTA值
#     def collect_categorical_values(self, file_paths):
#         cw_values = []
#         rigsta_values = []
#         for file_path in file_paths:
#             data = pd.read_csv(file_path)
#             if 'CW' in data.columns and 'RIGSTA' in data.columns:
#                 cw_values.extend(data['CW'].dropna().unique().tolist())  # 去除空值，收集唯一值
#                 rigsta_values.extend(data['RIGSTA'].dropna().unique().tolist())
#         return cw_values, rigsta_values
#
#     # 获取最大长度
#     def get_max_length(self, file_paths):
#         max_length = 0
#         for file_path in file_paths:
#             time_series, non_temp = self.load_time_series_data(file_path)
#             if len(time_series) > max_length:
#                 max_length = len(time_series)
#         return max_length
#
#     def __len__(self):
#         return len(self.file_paths)
#
#     def __getitem__(self, idx):
#         file_path = self.file_paths[idx]
#         label = self.labels[idx]
#         time_series, non_temp = self.load_time_series_data(file_path)
#         # 补0操作
#         length = len(time_series)
#         if length < self.max_seq_len:
#             padded_time_series = np.zeros((self.max_seq_len, 9))
#             padded_time_series[:length] = time_series
#         else:
#             padded_time_series = time_series[:self.max_seq_len]
#         # 对 非时序信息 进行补零操作
#         if length < self.max_seq_len:
#             padded_non_temp = np.zeros((self.max_seq_len, non_temp.shape[1]))  # 假设 non_temp 的列数固定
#             padded_non_temp[:length] = non_temp
#         else:
#             padded_non_temp = non_temp[:self.max_seq_len]
#
#         self.scaler.fit(padded_time_series)
#         padded_time_series = self.scaler.transform(padded_time_series)
#
#         return padded_time_series, padded_non_temp, label, file_path
#
#
# class EarlysignalPred(Dataset):
#     def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
#         self.root_path = root_path
#         self.scaler = StandardScaler()
#         self.file_names = [f for f in os.listdir(root_path) if f.endswith('.csv')]
#
#     def __len__(self):
#         return len(self.file_names)
#
#     def __getitem__(self, idx):
#         if torch.is_tensor(idx):
#             idx = idx.tolist()
#         file_name = self.file_names[idx]
#         file_path = os.path.join(self.root_path, self.file_names[idx])
#         data = pd.read_csv(file_path)
#         if 'CSIP' in data.columns:
#             data = data.drop(columns=['CSIP'])
#
#         # 假设每个 CSV 文件中的时间序列数据不包含标签，并且以 NumPy 数组形式返回
#         time_series = data.drop(['date'], axis=1).values
#         stamp = data['date']
#         # 补0操作
#         length = len(time_series)
#         if length < 152:
#             padded_time_series = np.zeros((152, 9))
#             padded_time_series[:length] = time_series
#         else:
#             padded_time_series = time_series[:152]
#
#         self.scaler.fit(padded_time_series)
#         padded_time_series = self.scaler.transform(padded_time_series)
#         # 将时间序列数据转换为 PyTorch 张量
#         padded_time_series = torch.tensor(padded_time_series, dtype=torch.float32)
#
#
#         return padded_time_series, file_name

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder, StandardScaler
class EarlysignaldetLoader(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.flag = flag
        # Step 1: 获取所有文件路径
        self.file_paths, self.labels = self.get_file_paths_and_labels(root_path)

        # Step 2: 收集所有文件中的CW和RIGSTA值，统一训练LabelEncoder
        self.cw_values, self.rigsta_values = self.collect_categorical_values(self.file_paths)

        # 初始化LabelEncoder并训练
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()
        self.cw_encoder.fit(self.cw_values)
        self.rigsta_encoder.fit(self.rigsta_values)

        self.max_seq_len = self.get_max_length(self.file_paths)

        self.train_file, self.test_file, self.train_labels, self.test_labels = train_test_split(
            self.file_paths, self.labels, test_size=0.2, random_state=42
        )
        if self.flag == 'TRAIN':
            self.file_paths = self.train_file
            self.labels = self.train_labels
            self.class_names = ['normal', 'earlysignal2']
        else:
            self.file_paths = self.test_file
            self.labels = self.test_labels
            self.class_names = ['normal', 'earlysignal2']
        self.scaler = StandardScaler()
        # self.transform = transform

    # 获取文件路径和标签
    def get_file_paths_and_labels(self, base_folder):
        file_paths = []
        labels = []
        for class_label, class_folder in enumerate(['normal', 'earlysignal2']):
            class_folder_path = os.path.join(base_folder, class_folder)
            for file_name in os.listdir(class_folder_path):
                file_paths.append(os.path.join(class_folder_path, file_name))
                labels.append(class_label)
        return file_paths, labels

    # 定义数据加载函数
    def load_time_series_data(self, file_path):
        data = pd.read_csv(file_path)
        # 初始化 non_temp 为与 data 行数一致的两列全零
        non_temp = pd.DataFrame(np.zeros((data.shape[0], 2)), columns=['CW', 'RIGSTA'])
        if 'CSIP' in data.columns:
            data = data.drop(columns=['CSIP'])
        if 'CW' in data.columns and 'RIGSTA' in data.columns:
            # 使用已训练好的LabelEncoder对CW和RIGSTA进行编码
            data['CW'] = self.cw_encoder.transform(data['CW'].fillna(method='ffill'))
            # 将RIGSTA列转换为字符串类型，并填充缺失值
            data['RIGSTA'] = data['RIGSTA'].astype(str).fillna(method='ffill')

            # 使用已训练好的LabelEncoder对RIGSTA进行编码
            data['RIGSTA'] = self.rigsta_encoder.transform(data['RIGSTA'])
            CW = data['CW']
            RIGSTA = data['RIGSTA']
            data = data.drop(columns=['CW', 'RIGSTA'])
            non_temp = pd.concat([CW, RIGSTA], axis=1)
        data = data.drop(['date'], axis=1)
        time_series = data.values
        non_temp = non_temp.values
        return time_series, non_temp

    # 收集所有文件中的CW和RIGSTA值
    def collect_categorical_values(self, file_paths):
        cw_values = []
        rigsta_values = []
        for file_path in file_paths:
            data = pd.read_csv(file_path)
            if 'CW' in data.columns and 'RIGSTA' in data.columns:
                cw_values.extend(data['CW'].dropna().unique().tolist())  # 去除空值，收集唯一值
                rigsta_values.extend(data['RIGSTA'].dropna().unique().tolist())
        return cw_values, rigsta_values

    # 获取最大长度
    def get_max_length(self, file_paths):
        max_length = 0
        for file_path in file_paths:
            time_series, non_temp = self.load_time_series_data(file_path)
            if len(time_series) > max_length:
                max_length = len(time_series)
        return max_length

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        file_path = self.file_paths[idx]
        label = self.labels[idx]
        time_series, non_temp = self.load_time_series_data(file_path)
        # 补0操作
        length = len(time_series)
        if length < self.max_seq_len:
            padded_time_series = np.zeros((self.max_seq_len, 9))
            padded_time_series[:length] = time_series
        else:
            padded_time_series = time_series[:self.max_seq_len]
        # 对 非时序信息 进行补零操作
        if length < self.max_seq_len:
            padded_non_temp = np.zeros((self.max_seq_len, non_temp.shape[1]))  # 假设 non_temp 的列数固定
            padded_non_temp[:length] = non_temp
        else:
            padded_non_temp = non_temp[:self.max_seq_len]

        self.scaler.fit(padded_time_series)
        padded_time_series = self.scaler.transform(padded_time_series)

        return padded_time_series, padded_non_temp, label, file_path


class EarlysignalPred(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.root_path = root_path
        self.scaler = StandardScaler()
        self.file_names = [f for f in os.listdir(root_path) if f.endswith('.csv')]

    def __len__(self):
        return len(self.file_names)

    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
        file_name = self.file_names[idx]
        file_path = os.path.join(self.root_path, self.file_names[idx])
        data = pd.read_csv(file_path)
        if 'CSIP' in data.columns:
            data = data.drop(columns=['CSIP'])

        # 假设每个 CSV 文件中的时间序列数据不包含标签，并且以 NumPy 数组形式返回
        time_series = data.drop(['date'], axis=1).values
        stamp = data['date']
        # 补0操作
        length = len(time_series)
        if length < 152:
            padded_time_series = np.zeros((152, 11))
            padded_time_series[:length] = time_series
        else:
            padded_time_series = time_series[:152]

        self.scaler.fit(padded_time_series)
        padded_time_series = self.scaler.transform(padded_time_series)
        # 将时间序列数据转换为 PyTorch 张量
        padded_time_series = torch.tensor(padded_time_series, dtype=torch.float32)


        return padded_time_series, file_name





class FluidEarlysignaldetection(Dataset):
    def __init__(self, root_path, file_list=None, limit_size=None, flag=None):
        self.flag = flag
        self.file_paths, self.labels = self.get_file_paths_and_labels(root_path)
        self.max_seq_len = self.get_max_length(self.file_paths)
        self.feature_df = self.file_paths

        self.train_file, self.test_file, self.train_labels, self.test_labels = train_test_split(self.file_paths, self.labels,
                                                                                        test_size=0.2, random_state=24)
        if self.flag == 'Train':
            self.file_paths = self.train_file
            self.labels = self.train_labels
            self.class_names = ['前驱', '正常数据']
        else:
            self.file_paths = self.test_file
            self.labels = self.test_labels
            self.class_names = ['前驱', '正常数据']
        self.scaler = StandardScaler()
        # self.transform = transform

    # 获取文件路径和标签
    def get_file_paths_and_labels(self, base_folder):
        file_paths = []
        labels = []
        for class_label, class_folder in enumerate(['正常数据', '前驱']):
            class_folder_path = os.path.join(base_folder, class_folder)
            for file_name in os.listdir(class_folder_path):
                file_paths.append(os.path.join(class_folder_path, file_name))
                labels.append(class_label)
        return file_paths, labels

    # 定义数据加载函数
    def load_time_series_data(self, file_path):
        data = pd.read_csv(file_path, encoding='ANSI')
        data = data.drop(['取样时间'], axis=1)
        time_series = data.values
        return time_series

    # 获取最大长度
    def get_max_length(self, file_paths):
        max_length = 0
        for file_path in file_paths:
            time_series = self.load_time_series_data(file_path)
            if len(time_series) > max_length:
                max_length = len(time_series)
        return max_length

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        file_path = self.file_paths[idx]
        label = self.labels[idx]
        time_series = self.load_time_series_data(file_path)
        # 补0操作
        length = len(time_series)
        if length < self.max_seq_len:
            padded_time_series = np.zeros((self.max_seq_len, 52))
            padded_time_series[:length] = time_series
        else:
            padded_time_series = time_series[:self.max_seq_len]

        self.scaler.fit(padded_time_series)
        padded_time_series = self.scaler.transform(padded_time_series)
        return padded_time_series, label, file_path


class EarlysignalAnomalyLoader(Dataset):
    """
    异常检测专用的前驱信号数据加载器
    训练阶段：仅使用normal数据进行无监督训练
    测试阶段：使用与前驱信号检测相同的测试数据
    """
    def __init__(self, root_path, win_size, step=1, flag="train"):
        self.flag = flag
        self.step = step
        self.win_size = win_size
        self.root_path = root_path
        self.scaler = StandardScaler()

        if flag == "train":
            # 训练阶段：仅使用normal数据
            self._load_train_data()
        else:
            # 测试阶段：使用与前驱信号检测相同的数据
            self._load_test_data()

        print(f"EarlysignalAnomalyLoader {flag}: {len(self.processed_data)} files loaded")

    def _setup_encoders_from_normal_data(self):
        """从normal数据中设置编码器和参数，避免循环引用"""
        normal_path = os.path.join(self.root_path, 'normal')

        # 收集CW和RIGSTA的唯一值
        cw_values = set()
        rigsta_values = set()
        all_data = []

        # 遍历normal文件夹收集数据
        if os.path.exists(normal_path):
            for file_name in os.listdir(normal_path):
                if file_name.endswith('.csv'):
                    file_path = os.path.join(normal_path, file_name)
                    df = pd.read_csv(file_path)

                    if 'CW' in df.columns:
                        cw_values.update(df['CW'].dropna().unique())
                    if 'RIGSTA' in df.columns:
                        rigsta_values.update(df['RIGSTA'].dropna().unique())

                    # 收集时序数据用于计算最大长度和标准化
                    time_series_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP']
                    time_series_data = df[time_series_cols].values
                    all_data.append(time_series_data)

        # 创建编码器
        self.cw_values = sorted(list(cw_values))
        self.rigsta_values = sorted(list(rigsta_values))
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()

        if self.cw_values:
            self.cw_encoder.fit(self.cw_values)
        if self.rigsta_values:
            self.rigsta_encoder.fit(self.rigsta_values)

        # 设置最大序列长度和标准化器
        if all_data:
            self.max_seq_len = max(len(data) for data in all_data)
            all_combined = np.vstack(all_data)
            self.scaler = StandardScaler()
            self.scaler.fit(all_combined)

    def _load_train_data(self):
        """加载训练数据：仅使用normal文件夹的数据"""
        normal_path = os.path.join(self.root_path, 'normal')

        # 收集CW和RIGSTA的唯一值
        cw_values = set()
        rigsta_values = set()

        # 先遍历收集唯一值
        if os.path.exists(normal_path):
            for file_name in os.listdir(normal_path):
                if file_name.endswith('.csv'):
                    file_path = os.path.join(normal_path, file_name)
                    df = pd.read_csv(file_path)
                    if 'CW' in df.columns:
                        cw_values.update(df['CW'].dropna().unique())
                    if 'RIGSTA' in df.columns:
                        rigsta_values.update(df['RIGSTA'].dropna().unique())

        # 创建编码器
        self.cw_values = sorted(list(cw_values))
        self.rigsta_values = sorted(list(rigsta_values))
        self.cw_encoder = LabelEncoder()
        self.rigsta_encoder = LabelEncoder()

        if self.cw_values:
            self.cw_encoder.fit(self.cw_values)
        if self.rigsta_values:
            self.rigsta_encoder.fit(self.rigsta_values)

        # 处理normal文件夹的数据
        self.processed_data = []
        self.processed_file_paths = []
        all_data = []

        if os.path.exists(normal_path):
            for file_name in os.listdir(normal_path):
                if file_name.endswith('.csv'):
                    file_path = os.path.join(normal_path, file_name)
                    combined_data = self._process_single_file(file_path)
                    all_data.append(combined_data)
                    self.processed_file_paths.append(file_path)

        # 找到最大序列长度
        if all_data:
            self.max_seq_len = max(len(data) for data in all_data)

            # 标准化所有数据
            all_combined = np.vstack(all_data)
            self.scaler.fit(all_combined)

            # 补零和标准化
            for data in all_data:
                if len(data) < self.max_seq_len:
                    padded_data = np.zeros((self.max_seq_len, data.shape[1]))
                    padded_data[:len(data)] = data
                else:
                    padded_data = data[:self.max_seq_len]

                padded_data = self.scaler.transform(padded_data)
                self.processed_data.append(padded_data)

    def _load_test_data(self):
        """加载测试数据：使用测试文件夹的实际预测数据"""
        # 先从normal数据中获取编码器和最大长度（避免循环引用）
        self._setup_encoders_from_normal_data()

        # 复用训练时的编码器和参数已在_setup_encoders_from_normal_data中设置

        # 加载测试文件夹的数据
        test_path = os.path.join(self.root_path, '测试')

        self.processed_data = []
        self.processed_labels = []  # 测试数据标签未知，设为-1
        self.processed_file_paths = []

        if os.path.exists(test_path):
            for file_name in os.listdir(test_path):
                if file_name.endswith('.csv'):
                    file_path = os.path.join(test_path, file_name)

                    # 处理单个文件
                    combined_data = self._process_single_file(file_path)

                    # 补零到最大长度
                    if len(combined_data) < self.max_seq_len:
                        padded_data = np.zeros((self.max_seq_len, combined_data.shape[1]))
                        padded_data[:len(combined_data)] = combined_data
                    else:
                        padded_data = combined_data[:self.max_seq_len]

                    # 标准化时序特征
                    time_series_part = padded_data[:, :9]
                    non_temp_part = padded_data[:, 9:]

                    time_series_part = self.scaler.transform(time_series_part)
                    final_data = np.concatenate([time_series_part, non_temp_part], axis=-1)

                    self.processed_data.append(final_data)
                    self.processed_labels.append(-1)  # 测试数据标签未知
                    self.processed_file_paths.append(file_path)

    def _process_single_file(self, file_path):
        """处理单个CSV文件"""
        df = pd.read_csv(file_path)

        # 时序特征
        time_series_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP']
        time_series_data = df[time_series_cols].values

        # 非时序特征
        non_temp_data = np.zeros((len(df), 2))

        if 'CW' in df.columns and hasattr(self, 'cw_encoder'):
            cw_filled = df['CW'].fillna(method='ffill').fillna(method='bfill')
            non_temp_data[:, 0] = self.cw_encoder.transform(cw_filled)

        if 'RIGSTA' in df.columns and hasattr(self, 'rigsta_encoder'):
            rigsta_filled = df['RIGSTA'].fillna(method='ffill').fillna(method='bfill')
            non_temp_data[:, 1] = self.rigsta_encoder.transform(rigsta_filled)

        # 合并特征
        combined_data = np.concatenate([time_series_data, non_temp_data], axis=-1)
        return combined_data

    def __len__(self):
        total_windows = 0
        for data in self.processed_data:
            total_windows += max(1, (data.shape[0] - self.win_size) // self.step + 1)
        return total_windows

    def __getitem__(self, index):
        # 找到对应的文件和窗口位置
        current_idx = 0
        for file_idx, data in enumerate(self.processed_data):
            file_windows = max(1, (data.shape[0] - self.win_size) // self.step + 1)
            if current_idx + file_windows > index:
                window_idx = index - current_idx
                start_idx = min(window_idx * self.step, data.shape[0] - self.win_size)
                start_idx = max(0, start_idx)
                end_idx = start_idx + self.win_size

                window_data = data[start_idx:end_idx]

                # 训练阶段返回正常标签，测试阶段返回真实标签
                if self.flag == "train":
                    window_labels = np.zeros(self.win_size)  # 训练时全为正常
                else:
                    window_labels = np.full(self.win_size, self.processed_labels[file_idx])

                return np.float32(window_data), np.float32(window_labels)
            current_idx += file_windows

        # 如果索引超出范围，返回最后一个窗口
        last_data = self.processed_data[-1]
        start_idx = max(0, last_data.shape[0] - self.win_size)
        window_data = last_data[start_idx:start_idx + self.win_size]

        if self.flag == "train":
            window_labels = np.zeros(self.win_size)
        else:
            window_labels = np.full(self.win_size, self.processed_labels[-1])

        return np.float32(window_data), np.float32(window_labels)

    def get_file_info(self, index):
        """获取对应样本的文件信息，用于结果输出"""
        current_idx = 0
        for file_idx, data in enumerate(self.processed_data):
            file_windows = max(1, (data.shape[0] - self.win_size) // self.step + 1)
            if current_idx + file_windows > index:
                if self.flag == "train":
                    return self.processed_file_paths[file_idx], 0  # 训练时标签为0
                else:
                    return self.processed_file_paths[file_idx], self.processed_labels[file_idx]
            current_idx += file_windows

        # 如果索引超出范围，返回最后一个文件信息
        if self.flag == "train":
            return self.processed_file_paths[-1], 0
        else:
            return self.processed_file_paths[-1], self.processed_labels[-1]


class EarlysignalTestLoader(Dataset):
    """
    专门用于测试文件夹数据的加载器
    两种算法在测试阶段使用相同的数据
    """
    def __init__(self, root_path, flag='test'):
        self.root_path = root_path
        self.flag = flag

        # 先从训练数据中获取编码器和标准化器
        self._setup_encoders_and_scaler()

        # 加载测试数据
        self._load_test_data()

        print(f"EarlysignalTestLoader: {len(self.file_paths)} test files loaded")

    def _setup_encoders_and_scaler(self):
        """从训练数据中设置编码器和标准化器"""
        # 使用EarlysignaldetLoader获取训练数据的编码器
        train_loader = EarlysignaldetLoader(root_path=self.root_path, flag='TRAIN')

        self.cw_encoder = train_loader.cw_encoder
        self.rigsta_encoder = train_loader.rigsta_encoder
        self.max_seq_len = train_loader.max_seq_len
        self.scaler = train_loader.scaler

    def _load_test_data(self):
        """加载测试文件夹的数据"""
        test_path = os.path.join(self.root_path, '测试')

        self.file_paths = []
        self.labels = []  # 测试数据标签未知，设为-1

        if os.path.exists(test_path):
            for file_name in os.listdir(test_path):
                if file_name.endswith('.csv'):
                    file_path = os.path.join(test_path, file_name)
                    self.file_paths.append(file_path)
                    self.labels.append(-1)  # 测试数据标签未知

    def load_time_series_data(self, file_path):
        """加载单个文件的时序数据"""
        df = pd.read_csv(file_path)

        # 时序特征
        time_series_cols = ['DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD', 'RPM', 'TOR', 'SPP']
        time_series_data = df[time_series_cols].values

        # 非时序特征
        non_temp_data = np.zeros((len(df), 2))

        if 'CW' in df.columns and hasattr(self, 'cw_encoder'):
            cw_filled = df['CW'].fillna(method='ffill').fillna(method='bfill')
            non_temp_data[:, 0] = self.cw_encoder.transform(cw_filled)

        if 'RIGSTA' in df.columns and hasattr(self, 'rigsta_encoder'):
            rigsta_filled = df['RIGSTA'].fillna(method='ffill').fillna(method='bfill')
            non_temp_data[:, 1] = self.rigsta_encoder.transform(rigsta_filled)

        return time_series_data, non_temp_data

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        file_path = self.file_paths[idx]
        label = self.labels[idx]

        # 加载数据
        time_series, non_temp = self.load_time_series_data(file_path)

        # 补零到最大长度
        length = len(time_series)
        if length < self.max_seq_len:
            padded_time_series = np.zeros((self.max_seq_len, 9))
            padded_time_series[:length] = time_series
            padded_non_temp = np.zeros((self.max_seq_len, non_temp.shape[1]))
            padded_non_temp[:length] = non_temp
        else:
            padded_time_series = time_series[:self.max_seq_len]
            padded_non_temp = non_temp[:self.max_seq_len]

        # 标准化
        scaler = StandardScaler()
        scaler.fit(padded_time_series)
        padded_time_series = scaler.transform(padded_time_series)

        return padded_time_series, padded_non_temp, label, file_path