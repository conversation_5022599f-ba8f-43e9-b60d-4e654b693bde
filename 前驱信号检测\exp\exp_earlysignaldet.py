from data_provider.data_factory import data_provider
from exp.exp_basic import Exp_Basic
from utils.tools import EarlyStopping, adjust_learning_rate, cal_accuracy
import torch.nn as nn
import pandas as pd
from torch import optim
import time
import warnings
import numpy as np
import os
import torch
import torch.nn.functional as F
from sklearn.metrics import precision_score, recall_score, f1_score, roc_curve, auc
import matplotlib.pyplot as plt
from torch.utils.data import Dataset, DataLoader
from data_provider.data_loader2 import EarlysignalPred
from utils.losses import FocalLoss
import pdb
import re
import os
import torch
import pandas as pd
from torch.utils.data import DataLoader
import torch.nn.functional as F

warnings.filterwarnings('ignore')


class Exp_Earlysignaldet(Exp_Basic):
    def __init__(self, args):
        super(Exp_Earlysignaldet, self).__init__(args)

    def _build_model(self):
        # model input depends on data
        train_data, train_loader = self._get_data(flag='TRAIN')
        test_data, test_loader = self._get_data(flag='TEST')
        self.args.seq_len = max(train_data.max_seq_len, test_data.max_seq_len)
        self.args.pred_len = 0
        # self.args.enc_in = 10
        self.args.num_class = 2
        # model init
        model = self.model_dict[self.args.model](self.args).float()
        if self.args.use_multi_gpu and self.args.use_gpu:
            model = nn.DataParallel(model, device_ids=self.args.device_ids)
        return model

    def _get_data(self, flag):
        data_set, data_loader = data_provider(self.args, flag)
        return data_set, data_loader

    def _select_optimizer(self):
        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)
        return model_optim

    def _select_criterion(self):
        criterion = nn.CrossEntropyLoss()
        criterion_name = 'CrossEntropyLoss'
        # criterion = FocalLoss(num_class=self.args.num_class)
        # criterion_name = 'FocalLoss'
        return criterion, criterion_name

    def vali(self, vali_data, vali_loader, criterion):
        total_loss = []
        preds = []
        trues = []
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, non_temp, label, file_name) in enumerate(vali_loader):
                batch_x = batch_x.float().to(self.device)
                label = label.to(self.device)

                outputs = self.model(batch_x, None, None, None)

                pred = outputs.detach().cpu()
                loss = criterion(pred, label.long().squeeze().cpu())
                total_loss.append(loss)

                preds.append(outputs.detach())
                trues.append(label)

        total_loss = np.average(total_loss)

        preds = torch.cat(preds, 0)
        trues = torch.cat(trues, 0)
        probs = torch.nn.functional.softmax(preds)  # (total_samples, num_classes) est. prob. for each class and sample
        predictions = torch.argmax(probs, dim=1).cpu().numpy()  # (total_samples,) int class index for each sample
        trues = trues.flatten().cpu().numpy()
        accuracy = cal_accuracy(predictions, trues)

        self.model.train()
        return total_loss, accuracy

    def train(self, setting):
        train_data, train_loader = self._get_data(flag='TRAIN')
        vali_data, vali_loader = self._get_data(flag='TEST')
        test_data, test_loader = self._get_data(flag='TEST')

        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)

        time_now = time.time()

        train_steps = len(train_loader)
        early_stopping = EarlyStopping(patience=self.args.patience, verbose=True)

        model_optim = self._select_optimizer()
        criterion, criterion_name = self._select_criterion()

        # 初始化存储损失值的列表
        train_losses = []
        vali_losses = []

        for epoch in range(self.args.train_epochs):
            iter_count = 0
            train_loss = []

            self.model.train()
            epoch_time = time.time()

            for i, (batch_x, non_temp, label, file_name) in enumerate(train_loader):
                iter_count += 1
                model_optim.zero_grad()

                non_temp = non_temp.float().to(self.device)
                batch_x = batch_x.float().to(self.device)

                label = label.to(self.device)

                outputs = self.model(batch_x, None, non_temp, None)
                if criterion_name == 'CrossEntropyLoss':
                    loss = criterion(outputs, label.long().squeeze(-1))
                elif criterion_name == 'FocalLoss':
                    loss = criterion(outputs, label.long().squeeze(-1))

                train_loss.append(loss.item())

                if (i + 1) % 100 == 0:
                    print("\titers: {0}, epoch: {1} | loss: {2:.7f}".format(i + 1, epoch + 1, loss.item()))
                    speed = (time.time() - time_now) / iter_count
                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)
                    print('\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))
                    iter_count = 0
                    time_now = time.time()

                loss.backward()
                nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=4.0)
                model_optim.step()

            print("Epoch: {} cost time: {}".format(epoch + 1, time.time() - epoch_time))
            train_loss = np.average(train_loss)
            vali_loss, val_accuracy = self.vali(vali_data, vali_loader, criterion)
            test_loss, test_accuracy = self.vali(test_data, test_loader, criterion)

            print(
                "Epoch: {0}, Steps: {1} | Train Loss: {2:.3f} Vali Loss: {3:.3f} Vali Acc: {4:.3f} Test Loss: {5:.3f} Test Acc: {6:.3f}"
                .format(epoch + 1, train_steps, train_loss, vali_loss, val_accuracy, test_loss, test_accuracy))
            early_stopping(-val_accuracy, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break
            if (epoch + 1) % 5 == 0:
                adjust_learning_rate(model_optim, epoch + 1, self.args)

            # 将每个epoch的平均损失值添加到列表中
            train_losses.append(train_loss)
            vali_losses.append(vali_loss)

        best_model_path = path + '/' + 'checkpoint.pth'
        self.model.load_state_dict(torch.load(best_model_path))

        # 绘制损失值图表
        plt.figure(figsize=(10, 5))
        plt.plot(train_losses, label='Training Loss')
        plt.plot(vali_losses, label='Validation Loss')
        plt.title('Training and Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.savefig(os.path.join(path, 'loss_plot.png'))
        plt.show()

        return self.model



    def cal_accuracy(self, predictions, trues):
        return (predictions == trues).sum() / len(trues)

    def test(self, setting, test=1):
        test_data, test_loader = self._get_data(flag='TEST')
        if test:
            print('loading model')
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))

        preds = []
        trues = []
        folder_path = './test_results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        self.model.eval()
        prediction_results = []
        with torch.no_grad():
            for i, (batch_x, non_temp, label, filename) in enumerate(test_loader):
                batch_x = batch_x.float().to(self.device)
                label = label.to(self.device)

                outputs = self.model(batch_x, None, None, None)

                preds.append(outputs.detach())
                prob = F.softmax(outputs, dim=1)
                risk = prob[:,1]
                _, pred = torch.max(prob.data, 1)
                trues.append(label)
                for j in range(batch_x.size(0)):  # batch_x.size(0) == 16
                    prediction_results.append([filename[j], risk[j].item(), pred[j].item(), label[j].item()])


        preds = torch.cat(preds, 0)
        trues = torch.cat(trues, 0)
        print('test shape:', preds.shape, trues.shape)

        probs = F.softmax(preds, dim=1)  # (total_samples, num_classes) est. prob. for each class and sample
        predictions = torch.argmax(probs, dim=1).cpu().numpy()  # (total_samples,) int class index for each sample
        trues = trues.flatten().cpu().numpy()
        accuracy = cal_accuracy(predictions, trues)

        # Calculate precision, recall, f1-score
        precision = precision_score(trues, predictions, average='weighted')
        recall = recall_score(trues, predictions, average='weighted')
        f1 = f1_score(trues, predictions, average='weighted')

        # result save
        folder_path = './results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        output_df = pd.DataFrame(prediction_results, columns=['Filename', 'Risk', 'Predicted_Label','GroundTruth'])
        output_df.to_csv('predictions' + self.args.model + '.csv', index=False, encoding='gbk')

        print('accuracy:{}'.format(accuracy))
        print('precision:{}'.format(precision))
        print('recall:{}'.format(recall))
        print('f1-score:{}'.format(f1))

        # 计算并绘制ROC和AUC
        predicted_probs = np.array([probs[i][1].cpu().numpy() for i in range(len(probs))])
        fpr, tpr, _ = roc_curve(trues, predicted_probs)
        roc_auc = auc(fpr, tpr)

        plt.figure()
        plt.plot(fpr, tpr, color='darkorange', lw=2, label='ROC curve (area = %0.2f)' % roc_auc)
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic')
        plt.legend(loc="lower right")
        plt.savefig(folder_path + 'roc_auc_curve.png')

        file_name = 'result_classification.txt'
        with open(os.path.join(folder_path, file_name), 'a') as f:
            f.write(setting + "  \n")
            f.write('accuracy:{}'.format(accuracy))
            f.write('\n')
            f.write('precision:{}'.format(precision))
            f.write('\n')
            f.write('recall:{}'.format(recall))
            f.write('\n')
            f.write('f1-score:{}'.format(f1))
            f.write('\n\n')

        return

    def predict(self, setting, load=True):
        # 使用测试文件夹数据
        from data_provider.data_factory import data_provider

        # 临时修改参数以使用测试数据加载器
        original_data = self.args.data
        self.args.data = 'EarlysignalTest'

        pred_dataset, pred_loader = data_provider(self.args, flag='test')

        # 恢复原始数据类型
        self.args.data = original_data

        # 加载模型
        if load:
            path = os.path.join(self.args.checkpoints, setting)
            best_model_path = path + '/' + 'checkpoint.pth'
            self.model.load_state_dict(torch.load(best_model_path))

        predictions = []

        # 创建结果保存目录
        folder_path = './test_results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        # 模型预测
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, non_temp, label, filename) in enumerate(pred_loader):
                batch_x = batch_x.float().to(self.device)
                # non_temp = non_temp.float().to(self.device)  # 如果模型需要非时序特征

                output = self.model(batch_x, None, None, None)
                probs = F.softmax(output, dim=1)
                risk = probs[0, 1]
                _, pred = torch.max(probs.data, 1)

                file_str = str(filename[0]) if isinstance(filename, (list, tuple)) else str(filename)
                predictions.append((file_str, risk.item(), pred.item()))

        # 排序逻辑调整
        def extract_well_number(name):
            """
            提取井号的数字部分，用于排序
            假设井号格式为 'X井'，其中 X 是数字
            """
            name = str(name)
            match = re.search(r'(\d+)井', name)  # e.g., 匹配 '1井'，提取出1作为井号数字
            return int(match.group(1)) if match else float('inf')  # 无匹配时返回inf

        def extract_time(name):
            """
            提取时间部分，假设时间格式为 '_YYYY-MM-DD_HH-MM-SS'
            """
            name = str(name)
            match = re.search(r'_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})', name)
            return match.group(1) if match else ""

        # 按井号数字排序，然后按时间排序
        predictions.sort(key=lambda x: (extract_well_number(x[0]), extract_time(x[0])))

        # 保存结果到CSV文件
        output_df = pd.DataFrame(predictions, columns=['Filename', 'Risk', 'Predicted_Label'])
        output_df.to_csv('predictions.csv', index=False, encoding='gbk')
        print("预测完毕，结果已保存至 predictions.csv")

        return predictions
    # def predict(self, setting, load=True):
    #     pred_dataset = EarlysignalPred(r"D:\\卡钻\Time-Series-Library-main\dataset2\predicate")
    #     pred_loader = DataLoader(pred_dataset, batch_size=1, shuffle=False)
    #
    #     if load:
    #         path = os.path.join(self.args.checkpoints, setting)
    #         best_model_path = path + '/' + 'checkpoint.pth'
    #         self.model.load_state_dict(torch.load(best_model_path))
    #
    #     predictions = []
    #
    #     folder_path = './test_results/' + setting + '/'
    #     if not os.path.exists(folder_path):
    #         os.makedirs(folder_path)

        # self.model.eval()
        # with torch.no_grad():
        #     for i, (batch_x, filename) in enumerate(pred_loader):
        #         batch_x = batch_x.float().to(self.device)
        #
        #         output = self.model(batch_x, None, None, None)
        #         probs = F.softmax(output, dim=1)
        #         risk = probs[0,1]
        #         _, pred = torch.max(probs.data, 1)
        #
        #         predictions.append((filename, risk.item(), pred.item()))
        # # 保存预测结果到CSV文件
        # output_df = pd.DataFrame(predictions, columns=['Filename', 'Risk', 'Predicted_Label'])
        # output_df.to_csv('predictions.csv', index=False, encoding='gbk')
        # print("预测完毕")
        # return predictions
