"""
测试导入是否正常的简单脚本
"""
import sys
import os

print("Testing imports...")

try:
    from exp.exp_earlysignaldet import Exp_Earlysignaldet
    print("✅ Exp_Earlysignaldet imported successfully")
except Exception as e:
    print(f"❌ Failed to import Exp_Earlysignaldet: {e}")

try:
    from exp.exp_earlysignal_anomaly import Exp_EarlysignalAnomaly
    print("✅ Exp_EarlysignalAnomaly imported successfully")
except Exception as e:
    print(f"❌ Failed to import Exp_EarlysignalAnomaly: {e}")

try:
    from data_provider.data_loader import EarlysignaldetLoader, EarlysignalAnomalyLoader, EarlysignalTestLoader
    print("✅ Data loaders imported successfully")
except Exception as e:
    print(f"❌ Failed to import data loaders: {e}")

try:
    from data_provider.data_factory import data_provider
    print("✅ data_provider imported successfully")
except Exception as e:
    print(f"❌ Failed to import data_provider: {e}")

print("Import test completed!")
