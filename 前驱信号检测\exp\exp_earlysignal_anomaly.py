import os
import torch
import numpy as np
import pandas as pd
from torch import nn
import torch.nn.functional as F
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score, roc_curve
import matplotlib.pyplot as plt
from exp.exp_anomaly_detection import Exp_Anomaly_Detection
from data_provider.data_factory import data_provider
from utils.tools import adjustment


class Exp_EarlysignalAnomaly(Exp_Anomaly_Detection):
    """
    前驱信号异常检测实验类
    继承异常检测基类，修改输出格式以匹配前驱信号检测需求
    """
    
    def __init__(self, args):
        super(Exp_EarlysignalAnomaly, self).__init__(args)
        
    def _get_data(self, flag):
        """重写数据获取方法，使用EarlysignalAnomalyLoader"""
        data_set, data_loader = data_provider(self.args, flag)
        return data_set, data_loader

    def test(self, setting, test=0):
        """
        重写测试方法，使用测试文件夹数据，输出与前驱信号检测一致的结果格式
        """
        # 修改数据类型以使用测试文件夹
        original_data = self.args.data
        self.args.data = 'EarlysignalAnomaly'  # 确保使用异常检测数据加载器

        test_data, test_loader = self._get_data(flag='test')
        train_data, train_loader = self._get_data(flag='train')

        # 恢复原始数据类型
        self.args.data = original_data
        
        if test:
            print('loading model')
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))

        # 创建结果保存目录
        folder_path = './test_results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        self.model.eval()
        self.anomaly_criterion = nn.MSELoss(reduce=False)

        # (1) 在训练集上统计正常模式
        attens_energy = []
        with torch.no_grad():
            for i, (batch_x, batch_y) in enumerate(train_loader):
                batch_x = batch_x.float().to(self.device)
                # 重构
                outputs = self.model(batch_x, None, None, None)
                # 计算重构误差
                score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
                score = score.detach().cpu().numpy()
                attens_energy.append(score)

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        train_energy = np.array(attens_energy)

        # (2) 在测试集上计算异常分数并找到阈值
        attens_energy = []
        test_labels = []
        test_file_info = []
        
        for i, (batch_x, batch_y) in enumerate(test_loader):
            batch_x = batch_x.float().to(self.device)
            # 重构
            outputs = self.model(batch_x, None, None, None)
            # 计算重构误差
            score = torch.mean(self.anomaly_criterion(batch_x, outputs), dim=-1)
            score = score.detach().cpu().numpy()
            attens_energy.append(score)
            test_labels.append(batch_y)
            
            # 获取文件信息
            for j in range(len(batch_x)):
                global_idx = i * self.args.batch_size + j
                if hasattr(test_data, 'get_file_info'):
                    file_path, file_label = test_data.get_file_info(global_idx)
                    test_file_info.append((file_path, file_label))

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        test_energy = np.array(attens_energy)
        combined_energy = np.concatenate([train_energy, test_energy], axis=0)
        threshold = np.percentile(combined_energy, 100 - self.args.anomaly_ratio)
        print("Threshold :", threshold)

        # (3) 基于阈值进行异常检测
        pred = (test_energy > threshold).astype(int)
        test_labels = np.concatenate(test_labels, axis=0).reshape(-1)
        test_labels = np.array(test_labels)
        gt = test_labels.astype(int)

        print("pred:   ", pred.shape)
        print("gt:     ", gt.shape)

        # (4) 检测调整
        gt, pred = adjustment(gt, pred)

        pred = np.array(pred)
        gt = np.array(gt)
        print("pred: ", pred.shape)
        print("gt:   ", gt.shape)

        # (5) 计算评估指标
        accuracy = accuracy_score(gt, pred)
        precision, recall, f_score, support = precision_recall_fscore_support(gt, pred, average='binary')
        
        # 计算ROC-AUC
        try:
            auc_score = roc_auc_score(gt, test_energy)
            fpr, tpr, _ = roc_curve(gt, test_energy)
        except:
            auc_score = 0.0
            fpr, tpr = [0, 1], [0, 1]

        print("Accuracy : {:0.4f}, Precision : {:0.4f}, Recall : {:0.4f}, F-score : {:0.4f}, AUC : {:0.4f}".format(
            accuracy, precision, recall, f_score, auc_score))

        # (6) 保存详细结果 - 与前驱信号检测格式一致
        results_df = []
        
        # 聚合每个文件的结果
        file_results = {}
        for i, (file_path, file_label) in enumerate(test_file_info):
            if i < len(test_energy):
                file_name = os.path.basename(file_path)
                if file_name not in file_results:
                    file_results[file_name] = {
                        'anomaly_scores': [],
                        'predictions': [],
                        'true_label': file_label,
                        'file_path': file_path
                    }
                
                file_results[file_name]['anomaly_scores'].append(test_energy[i])
                file_results[file_name]['predictions'].append(pred[i] if i < len(pred) else 0)
        
        # 为每个文件计算最终结果
        for file_name, info in file_results.items():
            avg_anomaly_score = np.mean(info['anomaly_scores'])
            max_anomaly_score = np.max(info['anomaly_scores'])
            final_prediction = 1 if max_anomaly_score > threshold else 0
            
            # 计算风险值 (0-1之间)
            risk_value = min(max_anomaly_score / (threshold * 2), 1.0)
            
            results_df.append({
                'file_name': file_name,
                'risk_value': risk_value,
                'predicted_label': final_prediction,
                'true_label': info['true_label'],
                'avg_anomaly_score': avg_anomaly_score,
                'max_anomaly_score': max_anomaly_score,
                'threshold': threshold
            })
        
        # 保存结果到CSV
        results_df = pd.DataFrame(results_df)
        results_path = os.path.join(folder_path, 'earlysignal_anomaly_results.csv')
        results_df.to_csv(results_path, index=False)
        print(f"Results saved to: {results_path}")

        # (7) 保存性能指标
        metrics_file = os.path.join(folder_path, 'performance_metrics.txt')
        with open(metrics_file, 'w') as f:
            f.write(f"Setting: {setting}\n")
            f.write(f"Accuracy: {accuracy:.4f}\n")
            f.write(f"Precision: {precision:.4f}\n")
            f.write(f"Recall: {recall:.4f}\n")
            f.write(f"F1-Score: {f_score:.4f}\n")
            f.write(f"AUC: {auc_score:.4f}\n")
            f.write(f"Threshold: {threshold:.4f}\n")
            f.write(f"Total Files: {len(results_df)}\n")
            f.write(f"Anomaly Files: {len(results_df[results_df['predicted_label'] == 1])}\n")

        # (8) 绘制ROC曲线
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {auc_score:.2f})')
        plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('Receiver Operating Characteristic - Earlysignal Anomaly Detection')
        plt.legend(loc="lower right")
        plt.savefig(os.path.join(folder_path, 'roc_curve.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # (9) 保存到全局结果文件
        global_result_file = "result_earlysignal_anomaly.txt"
        with open(global_result_file, 'a') as f:
            f.write(f"{setting}\n")
            f.write(f"Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F-score: {f_score:.4f}, AUC: {auc_score:.4f}\n")
            f.write('\n')

        return accuracy, precision, recall, f_score, auc_score
