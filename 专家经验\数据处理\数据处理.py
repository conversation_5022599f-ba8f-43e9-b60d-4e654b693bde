import os
import pandas as pd

# 输入和输出路径
input_folder = "30口井"
output_folder = "converted_csv"
os.makedirs(output_folder, exist_ok=True)

# ===== 2. 设置字段映射（中文 -> 英文）=====
column_mapping = {
    "井深": "DEP",
    "钻头位置": "BITDEP",
    "大钩高度": "HOKHEI",
    "大钩负荷": "HKLD",
    "转盘转速": "RPM",
    "扭矩": "TOR",
    "入口流量": "FLOWIN",
    "出口流量": "FLOWOUT",
    "立压": "SPP"
}

# 读取函数，支持 .xls 或伪装成 .xls 的 .csv
def smart_read(file_path):
    try:
        return pd.read_excel(file_path, engine="xlrd")
    except Exception:
        try:
            return pd.read_csv(file_path, encoding="utf-8")
        except Exception as e_csv:
            raise ValueError(f"❌ 无法读取：{file_path}，错误：{e_csv}")

# 遍历处理所有文件
for file in os.listdir(input_folder):
    if file.endswith(".xls"):
        file_path = os.path.join(input_folder, file)
        print(f"🔄 正在处理文件：{file_path}")
        try:
            df = smart_read(file_path)

            # 日期和时间列自动查找
            date_col = next((col for col in df.columns if "日期" in col), None)
            time_col = next((col for col in df.columns if "时间" in col), None)

            if date_col and time_col:
                df["date"] = df[date_col].astype(str).str.strip() + " " + df[time_col].astype(str).str.strip()
            else:
                raise ValueError("❌ 找不到 日期/时间 列")

            # 提取并重命名所需列
            available_cols = {k: v for k, v in column_mapping.items() if k in df.columns}
            df_extracted = df[list(available_cols.keys()) + ["date"]].rename(columns=available_cols)

            # 输出文件名
            out_name = os.path.splitext(file)[0] + ".csv"
            out_path = os.path.join(output_folder, out_name)
            df_extracted.to_csv(out_path, index=False, encoding="utf-8-sig")

            print(f"✅ 成功转换：{out_path}")
        except Exception as e:
            print(f"❌ 处理失败：{file}，错误信息：{e}")
