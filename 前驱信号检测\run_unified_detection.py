import argparse
import os
import torch
import random
import numpy as np
import pandas as pd
from datetime import datetime

from exp.exp_earlysignaldet import Exp_Earlysignaldet
from exp.exp_earlysignal_anomaly import Exp_EarlysignalAnomaly


def set_seed(seed=42):
    """设置随机种子确保结果可重现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def create_base_args():
    """创建基础参数配置"""
    parser = argparse.ArgumentParser(description='Unified Early Signal and Anomaly Detection')
    
    # 基础设置
    parser.add_argument('--task_name', type=str, default='earlysignaldet', help='task name')
    parser.add_argument('--is_training', type=int, default=1, help='status')
    parser.add_argument('--model_id', type=str, default='unified_detection', help='model id')
    parser.add_argument('--model', type=str, default='TimesNet', help='model name')
    
    # 数据设置
    parser.add_argument('--data', type=str, default='Earlysignaldet', help='dataset type')
    parser.add_argument('--root_path', type=str, default='./dataset2/', help='root path of the data file')
    parser.add_argument('--data_path', type=str, default='', help='data file')
    parser.add_argument('--features', type=str, default='M', help='forecasting task, options:[M, S, MS]')
    parser.add_argument('--target', type=str, default='OT', help='target feature in S or MS task')
    parser.add_argument('--freq', type=str, default='h', help='freq for time features encoding')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='location of model checkpoints')
    
    # 模型设置
    parser.add_argument('--seq_len', type=int, default=96, help='input sequence length')
    parser.add_argument('--label_len', type=int, default=48, help='start token length')
    parser.add_argument('--pred_len', type=int, default=96, help='prediction sequence length')
    parser.add_argument('--seasonal_patterns', type=str, default='Monthly', help='subset for M4')
    parser.add_argument('--inverse', action='store_true', help='inverse output data', default=False)
    
    # 模型参数
    parser.add_argument('--top_k', type=int, default=5, help='for TimesBlock')
    parser.add_argument('--num_kernels', type=int, default=6, help='for Inception')
    parser.add_argument('--enc_in', type=int, default=11, help='encoder input size')
    parser.add_argument('--dec_in', type=int, default=11, help='decoder input size')
    parser.add_argument('--c_out', type=int, default=11, help='output size')
    parser.add_argument('--d_model', type=int, default=512, help='dimension of model')
    parser.add_argument('--n_heads', type=int, default=8, help='num of heads')
    parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')
    parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')
    parser.add_argument('--d_ff', type=int, default=2048, help='dimension of fcn')
    parser.add_argument('--moving_avg', type=int, default=25, help='window size of moving average')
    parser.add_argument('--factor', type=int, default=1, help='attn factor')
    parser.add_argument('--distil', action='store_false', help='whether to use distilling in encoder')
    parser.add_argument('--dropout', type=float, default=0.1, help='dropout')
    parser.add_argument('--embed', type=str, default='timeF', help='time features encoding')
    parser.add_argument('--activation', type=str, default='gelu', help='activation')
    parser.add_argument('--output_attention', action='store_true', help='whether to output attention in ecoder')
    
    # 训练设置
    parser.add_argument('--num_workers', type=int, default=10, help='data loader num workers')
    parser.add_argument('--itr', type=int, default=1, help='experiments times')
    parser.add_argument('--train_epochs', type=int, default=10, help='train epochs')
    parser.add_argument('--batch_size', type=int, default=32, help='batch size of train input data')
    parser.add_argument('--patience', type=int, default=3, help='early stopping patience')
    parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')
    parser.add_argument('--des', type=str, default='test', help='exp description')
    parser.add_argument('--loss', type=str, default='MSE', help='loss function')
    parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')
    parser.add_argument('--use_amp', action='store_true', help='use automatic mixed precision training', default=False)
    
    # GPU设置
    parser.add_argument('--use_gpu', type=bool, default=True, help='use gpu')
    parser.add_argument('--gpu', type=int, default=0, help='gpu')
    parser.add_argument('--use_multi_gpu', action='store_true', help='use multiple gpus', default=False)
    parser.add_argument('--devices', type=str, default='0,1,2,3', help='device ids of multile gpus')
    
    # 异常检测特定参数
    parser.add_argument('--anomaly_ratio', type=float, default=1.0, help='prior anomaly ratio (%)')
    parser.add_argument('--win_size', type=int, default=100, help='window size for anomaly detection')
    
    return parser


def run_earlysignal_detection(args):
    """运行前驱信号检测"""
    print("=" * 50)
    print("Running Early Signal Detection...")
    print("训练数据：normal + earlysignal2 文件夹")
    print("测试数据：测试文件夹")
    print("=" * 50)

    # 设置前驱信号检测参数
    args.task_name = 'earlysignaldet'
    args.data = 'Earlysignaldet'
    args.enc_in = 11  # 9个时序特征 + 2个非时序特征
    args.dec_in = 11
    args.c_out = 2    # 二分类输出

    # 创建实验
    exp = Exp_Earlysignaldet(args)

    # 设置实验名称
    setting = f'{args.task_name}_{args.model}_{args.model_id}_{args.data}_{args.des}'

    if args.is_training:
        print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
        exp.train(setting)

        print('>>>>>>>predicting on test folder : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
        # 使用predict方法对测试文件夹进行预测
        exp.predict(setting, load=True)
        torch.cuda.empty_cache()
    else:
        print('>>>>>>>predicting on test folder : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
        exp.predict(setting, load=True)
        torch.cuda.empty_cache()

    return setting


def run_anomaly_detection(args):
    """运行异常检测"""
    print("=" * 50)
    print("Running Anomaly Detection...")
    print("训练数据：仅使用 normal 文件夹（无监督学习）")
    print("测试数据：测试文件夹（与前驱信号检测相同）")
    print("=" * 50)

    # 设置异常检测参数
    args.task_name = 'anomaly_detection'
    args.data = 'EarlysignalAnomaly'
    args.enc_in = 11  # 9个时序特征 + 2个非时序特征
    args.dec_in = 11
    args.c_out = 11   # 重构输出

    # 创建实验
    exp = Exp_EarlysignalAnomaly(args)

    # 设置实验名称
    setting = f'{args.task_name}_{args.model}_{args.model_id}_{args.data}_{args.des}'

    if args.is_training:
        print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))
        exp.train(setting)

        print('>>>>>>>testing on test folder : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
        exp.test(setting, test=1)
        torch.cuda.empty_cache()
    else:
        print('>>>>>>>testing on test folder : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))
        exp.test(setting, test=1)
        torch.cuda.empty_cache()

    return setting


def main():
    """主函数：统一运行两种检测算法"""
    # 设置随机种子
    set_seed(42)
    
    # 创建参数解析器
    parser = create_base_args()
    args = parser.parse_args()
    
    # 设置GPU
    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False
    if args.use_gpu and args.use_multi_gpu:
        args.devices = args.devices.replace(' ', '')
        device_ids = args.devices.split(',')
        args.device_ids = [int(id_) for id_ in device_ids]
        args.gpu = args.device_ids[0]
    
    print('Args in experiment:')
    print(args)
    
    # 创建结果汇总
    results_summary = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data_path': args.root_path,
        'model': args.model,
        'results': {}
    }
    
    try:
        # 1. 运行前驱信号检测
        earlysignal_setting = run_earlysignal_detection(args)
        results_summary['results']['earlysignal_detection'] = {
            'setting': earlysignal_setting,
            'status': 'completed'
        }
        
        # 2. 运行异常检测
        anomaly_setting = run_anomaly_detection(args)
        results_summary['results']['anomaly_detection'] = {
            'setting': anomaly_setting,
            'status': 'completed'
        }
        
        print("=" * 50)
        print("All Detection Tasks Completed Successfully!")
        print("=" * 50)
        print(f"Early Signal Detection Results: ./test_results/{earlysignal_setting}/")
        print(f"Anomaly Detection Results: ./test_results/{anomaly_setting}/")
        
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        results_summary['error'] = str(e)
    
    # 保存运行摘要
    summary_path = f"./unified_detection_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(summary_path, 'w') as f:
        f.write("Unified Detection Run Summary\n")
        f.write("=" * 50 + "\n")
        f.write(f"Timestamp: {results_summary['timestamp']}\n")
        f.write(f"Data Path: {results_summary['data_path']}\n")
        f.write(f"Model: {results_summary['model']}\n")
        f.write("\nResults:\n")
        for task, info in results_summary['results'].items():
            f.write(f"  {task}: {info['status']} ({info['setting']})\n")
        if 'error' in results_summary:
            f.write(f"\nError: {results_summary['error']}\n")
    
    print(f"Run summary saved to: {summary_path}")


if __name__ == '__main__':
    main()
