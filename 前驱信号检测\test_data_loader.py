"""
测试数据加载器的简单脚本
验证EarlysignalAnomalyLoader是否正确工作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_provider.data_loader import EarlysignaldetLoader, EarlysignalAnomalyLoader


def test_earlysignal_loader():
    """测试前驱信号检测数据加载器"""
    print("=" * 50)
    print("Testing EarlysignaldetLoader...")
    print("=" * 50)
    
    root_path = './dataset2/'
    
    # 测试训练集
    train_loader = EarlysignaldetLoader(root_path=root_path, flag='TRAIN')
    print(f"Train files: {len(train_loader.file_paths)}")
    print(f"Train labels: {len(train_loader.labels)}")
    print(f"Max sequence length: {train_loader.max_seq_len}")
    
    # 测试测试集
    test_loader = EarlysignaldetLoader(root_path=root_path, flag='TEST')
    print(f"Test files: {len(test_loader.file_paths)}")
    print(f"Test labels: {len(test_loader.labels)}")
    
    # 测试数据获取
    if len(train_loader) > 0:
        sample = train_loader[0]
        print(f"Sample data shapes: {[x.shape if hasattr(x, 'shape') else type(x) for x in sample]}")
    
    return train_loader, test_loader


def test_anomaly_loader():
    """测试异常检测数据加载器"""
    print("=" * 50)
    print("Testing EarlysignalAnomalyLoader...")
    print("=" * 50)
    
    root_path = './dataset2/'
    win_size = 100
    
    # 测试训练集（仅normal数据）
    train_loader = EarlysignalAnomalyLoader(
        root_path=root_path, 
        win_size=win_size, 
        flag='train'
    )
    print(f"Train windows: {len(train_loader)}")
    
    # 测试测试集（与前驱信号检测相同）
    test_loader = EarlysignalAnomalyLoader(
        root_path=root_path, 
        win_size=win_size, 
        flag='test'
    )
    print(f"Test windows: {len(test_loader)}")
    
    # 测试数据获取
    if len(train_loader) > 0:
        train_sample = train_loader[0]
        print(f"Train sample shapes: {[x.shape for x in train_sample]}")
        
    if len(test_loader) > 0:
        test_sample = test_loader[0]
        print(f"Test sample shapes: {[x.shape for x in test_sample]}")
        
        # 测试文件信息获取
        file_path, label = test_loader.get_file_info(0)
        print(f"File info: {os.path.basename(file_path)}, label: {label}")
    
    return train_loader, test_loader


def compare_test_data():
    """比较两种加载器的测试数据是否一致"""
    print("=" * 50)
    print("Comparing test data consistency...")
    print("=" * 50)
    
    root_path = './dataset2/'
    
    # 前驱信号检测测试数据
    earlysignal_test = EarlysignaldetLoader(root_path=root_path, flag='TEST')
    
    # 异常检测测试数据
    anomaly_test = EarlysignalAnomalyLoader(
        root_path=root_path, 
        win_size=100, 
        flag='test'
    )
    
    print(f"EarlysignaldetLoader test files: {len(earlysignal_test.file_paths)}")
    print(f"EarlysignalAnomalyLoader test files: {len(anomaly_test.processed_file_paths)}")
    
    # 检查文件路径是否一致
    earlysignal_files = set(os.path.basename(f) for f in earlysignal_test.file_paths)
    anomaly_files = set(os.path.basename(f) for f in anomaly_test.processed_file_paths)
    
    print(f"Common files: {len(earlysignal_files & anomaly_files)}")
    print(f"EarlysignaldetLoader only: {len(earlysignal_files - anomaly_files)}")
    print(f"AnomalyLoader only: {len(anomaly_files - earlysignal_files)}")
    
    if earlysignal_files == anomaly_files:
        print("✅ Test data files are consistent!")
    else:
        print("❌ Test data files are different!")
        print("Different files:", earlysignal_files.symmetric_difference(anomaly_files))


def main():
    """主测试函数"""
    try:
        # 检查数据目录是否存在
        if not os.path.exists('./dataset2/'):
            print("❌ Dataset directory './dataset2/' not found!")
            print("Please make sure the dataset is in the correct location.")
            return
        
        if not os.path.exists('./dataset2/normal/'):
            print("❌ Normal data directory './dataset2/normal/' not found!")
            return
            
        if not os.path.exists('./dataset2/earlysignal2/'):
            print("❌ Earlysignal data directory './dataset2/earlysignal2/' not found!")
            return
        
        # 测试前驱信号检测数据加载器
        earlysignal_train, earlysignal_test = test_earlysignal_loader()
        
        # 测试异常检测数据加载器
        anomaly_train, anomaly_test = test_anomaly_loader()
        
        # 比较测试数据一致性
        compare_test_data()
        
        print("=" * 50)
        print("All tests completed!")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
